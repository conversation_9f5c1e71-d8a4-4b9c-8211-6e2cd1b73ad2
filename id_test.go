package appioid_test

import (
	"encoding/json"
	"sort"
	"testing"
	"time"

	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		prefix  string
		wantErr string
	}{
		{
			name:   "OK",
			prefix: "prod",
		},
		{
			name:   "OK",
			prefix: "demo_prod",
		},
		{
			name:    "Too short prefix",
			prefix:  "p",
			wantErr: "the prefix does not have the right format: [a-z_]{2,10}",
		},
		{
			name:    "Too long prefix",
			prefix:  "abcdef<PERSON>ijk",
			wantErr: "the prefix does not have the right format: [a-z_]{2,10}",
		},
		{
			name:    "prefix with non-letters",
			prefix:  "pr1",
			wantErr: "the prefix does not have the right format: [a-z_]{2,10}",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				pID, err := appioid.New(tt.prefix)

				if tt.wantErr == "" {
					// ensure a format of string is correct
					assert.Regexp(t, `^[a-z_]+_[a-z\d]{26}$`, pID.String())

					// ensure we can reparse the string
					r, err := appioid.Parse(pID.String())
					assert.Nil(t, err)
					assert.Equal(t, r.String(), pID.String())
				} else {
					assert.Nil(t, pID)
					assert.NotNil(t, err)
					assert.Equal(t, tt.wantErr, err.Error())
				}
			},
		)
	}
}

func TestParse(t *testing.T) {
	tests := []struct {
		name     string
		id       string
		expected string
		want     string
		wantErr  string
	}{
		{
			name:     "OK",
			id:       "pro_01zbx4vsj8dem0dem0dem0dem0",
			expected: "pro_01zbx4vsj8dem0dem0dem0dem0",
		},
		{
			name:     "OK - max ULID",
			id:       "prod_7zzzzzzzzzzzzzzzzzzzzzzzzz",
			expected: "prod_7zzzzzzzzzzzzzzzzzzzzzzzzz",
		},
		{
			name:     "OK - uppercase",
			id:       "prod_7ZZZZZZZZZZZZZZZZZZZZZZZZZ",
			expected: "prod_7zzzzzzzzzzzzzzzzzzzzzzzzz",
		},
		{
			name:    "Error - invalid format",
			id:      "po_1gbwf7pezzb56efp2far9a678",
			wantErr: "the appio ID does not have the right format: abc_xxxxxxxxxxxxxxxxxxxxxxxxxx",
		},
		{
			name:    "Error - invalid format",
			id:      "abcdefjklmn_01zbx4vsj8dem0dem0dem0dem0",
			wantErr: "the appio ID does not have the right format: abc_xxxxxxxxxxxxxxxxxxxxxxxxxx",
		},
		{
			name:    "Error - invalid format",
			id:      "pro_1gbwf7pezzb56efp2far9a678",
			wantErr: "the appio ID does not have the right format: abc_xxxxxxxxxxxxxxxxxxxxxxxxxx",
		},
		{
			name:    "Error - overflow",
			id:      "pro_zzzzzzzzzzzzzzzzzzzzzzzzzz",
			wantErr: "ulid: overflow when unmarshaling",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				u, err := appioid.Parse(tt.id)
				if tt.wantErr == "" {
					assert.NotNil(t, u)
					assert.Equal(t, tt.expected, u.String())
					assert.NoError(t, err)
				} else {
					assert.Nil(t, u)
					assert.NotNil(t, err)
					assert.Equal(t, tt.wantErr, err.Error())
				}
			},
		)
	}
}

func TestMustParse(t *testing.T) {
	tests := []struct {
		name      string
		id        string
		want      string
		wantPanic string
	}{
		{
			name:      "OK",
			id:        "pro_01zbx4vsj8dem0dem0dem0dem0",
			wantPanic: "",
		},
		{
			name:      "OK - max ULID",
			id:        "pro_7zzzzzzzzzzzzzzzzzzzzzzzzz",
			wantPanic: "",
		},
		{
			name:      "Panic - invalid format",
			id:        "pro_1gbwf7pezzb56efp2far9a678",
			wantPanic: "appioID: Parse(pro_1gbwf7pezzb56efp2far9a678): the appio ID does not have the right format: abc_xxxxxxxxxxxxxxxxxxxxxxxxxx",
		},
		{
			name:      "Panic - overflow",
			id:        "pro_zzzzzzzzzzzzzzzzzzzzzzzzzz",
			wantPanic: "appioID: Parse(pro_zzzzzzzzzzzzzzzzzzzzzzzzzz): ulid: overflow when unmarshaling",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				defer func() {
					err := recover()
					if tt.wantPanic == "" {
						assert.Nil(t, err)
					} else {
						assert.Equal(t, tt.wantPanic, err)
					}
				}()

				u := appioid.MustParse(tt.id)
				if tt.wantPanic == "" {
					assert.Equal(t, tt.id, u.String())
				}
			},
		)
	}
}

func TestType(t *testing.T) {
	u, err := appioid.Parse("pro_01zbx4vsj8dem0dem0dem0dem0")
	assert.NoError(t, err)
	assert.Equal(t, "pro", u.Type())
}

func TestTime(t *testing.T) {
	u, err := appioid.Parse("pro_01zbx4vsj8dem0dem0dem0dem0")
	assert.NoError(t, err)
	assert.Equal(t, time.UnixMilli(2177452861000), u.Time())
}

func TestMarshal(t *testing.T) {
	u, err := appioid.Parse("svc_01zbx4vsj8dem0dem0dem0dem0")
	assert.NoError(t, err)

	testStruct := struct {
		ID   *appioid.ID `json:"id"`
		Name string      `json:"name"`
	}{
		ID:   u,
		Name: "test struct name",
	}

	bytes, err := json.Marshal(testStruct)
	assert.NoError(t, err)

	jsonStr := string(bytes)
	assert.NotEmpty(t, jsonStr)

	expected := `{"id":"svc_01zbx4vsj8dem0dem0dem0dem0","name":"test struct name"}`
	assert.Equal(t, expected, jsonStr)
}

func TestUnmarshal(t *testing.T) {
	jsonStr := `{"id":"svc_01zbx4vsj8dem0dem0dem0dem0","name":"test struct name"}`
	type testStruct struct {
		ID   *appioid.ID `json:"id"`
		Name string      `json:"name"`
	}
	tStruct := new(testStruct)
	err := json.Unmarshal([]byte(jsonStr), tStruct)
	assert.NoError(t, err)
	assert.Equal(t, "test struct name", tStruct.Name)
	assert.Equal(t, "svc_01zbx4vsj8dem0dem0dem0dem0", tStruct.ID.String())
}

func TestUnmarshalError(t *testing.T) {
	jsonStr := `{"id":"pro_XXX","name":"test struct name"}`
	type testStruct struct {
		ID   *appioid.ID `json:"id"`
		Name string      `json:"name"`
	}
	tStruct := new(testStruct)
	err := json.Unmarshal([]byte(jsonStr), tStruct)

	assert.Error(t, err)
	assert.ErrorIs(t, err, appioid.ErrorIDFormat)
}

func TestSorting(t *testing.T) {
	// the number of IDs to generate
	testSize := 10000

	order := make(map[string]int, testSize)
	ids := make([]string, 0, testSize)

	for i := 0; i < testSize; i++ {
		pID, err := appioid.New("inv")
		assert.Nil(t, err)

		// add to a slice so we can sort, and also a map so we can check the order later
		order[pID.String()] = i
		ids = append(ids, pID.String())
	}

	// reverse sort it - because why not
	sort.Sort(sort.Reverse(sort.StringSlice(ids)))

	// comment out this line to see it fail :)
	sort.Strings(ids)

	for i, id := range ids {
		assert.Equal(t, order[id], i)
		if order[id] != i {
			// if we fail at anypoint then quit early to save filling the output with a load of messages
			break
		}
	}
}

func TestID_Scan(t *testing.T) {
	var id appioid.ID
	err := id.Scan("svc_01zbx4vsj8dem0dem0dem0dem0")
	assert.NoError(t, err)

	err = id.Scan(111)
	assert.Error(t, err, "unsupported type: int")
}

func TestID_Equal(t *testing.T) {
	tests := []struct {
		name     string
		id1      *appioid.ID
		id2      *appioid.ID
		expected bool
	}{
		{
			name:     "Both nil",
			id1:      nil,
			id2:      nil,
			expected: true,
		},
		{
			name:     "First nil, second not nil",
			id1:      nil,
			id2:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			expected: false,
		},
		{
			name:     "First not nil, second nil",
			id1:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			id2:      nil,
			expected: false,
		},
		{
			name:     "Same ID string - should be equal",
			id1:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			id2:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			expected: true,
		},
		{
			name:     "Different ULID, same prefix - should not be equal",
			id1:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			id2:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem1"),
			expected: false,
		},
		{
			name:     "Same ULID, different prefix - should not be equal",
			id1:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			id2:      appioid.MustParse("usr_01zbx4vsj8dem0dem0dem0dem0"),
			expected: false,
		},
		{
			name:     "Completely different IDs - should not be equal",
			id1:      appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0"),
			id2:      appioid.MustParse("usr_01zbx4vsj8dem0dem0dem0dem1"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.id1.Equal(tt.id2)
			assert.Equal(t, tt.expected, result)

			// Test symmetry: id1.Equal(id2) should equal id2.Equal(id1)
			if tt.id1 != nil && tt.id2 != nil {
				reverseResult := tt.id2.Equal(tt.id1)
				assert.Equal(t, result, reverseResult, "Equal method should be symmetric")
			}
		})
	}
}

func TestID_Equal_SameInstance(t *testing.T) {
	// Test that an ID is equal to itself
	id := appioid.MustParse("svc_01zbx4vsj8dem0dem0dem0dem0")
	assert.True(t, id.Equal(id))
}

func TestID_Equal_ParsedFromSameString(t *testing.T) {
	// Test that two IDs parsed from the same string are equal
	idString := "svc_01zbx4vsj8dem0dem0dem0dem0"
	id1 := appioid.MustParse(idString)
	id2 := appioid.MustParse(idString)

	// They should be equal even though they are different instances
	assert.True(t, id1.Equal(id2))
	assert.True(t, id2.Equal(id1))

	// But they should not be the same pointer
	assert.False(t, id1 == id2, "Should be different instances")
}
