package appioid

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/oklog/ulid/v2"
)

var (
	prefixFormat       = regexp.MustCompile(`^[a-z_]{2,10}$`)
	idFormat           = regexp.MustCompile(`^[a-z_]{2,10}_[0-9a-hjkmnp-tv-z]{26}$`)
	ErrorIDFormat      = errors.New("the appio ID does not have the right format: abc_xxxxxxxxxxxxxxxxxxxxxxxxxx")
	ErrorInvalidPrefix = errors.New("the prefix does not have the right format: [a-z_]{2,10}")
)

type ID struct {
	prefix string
	value  ulid.ULID
}

// New creates a Appio short ID, given a prefix
func New(prefix string) (*ID, error) {
	if !prefixFormat.MatchString(prefix) {
		return nil, ErrorInvalidPrefix
	}

	return &ID{
		prefix: prefix,
		value:  ulid.Make(),
	}, nil
}

// Parse generates a new Appio short ID from a string, in the correct format,
// or returns an error.
func Parse(id string) (*ID, error) {
	id = strings.ToLower(id)

	if !idFormat.MatchString(id) {
		return nil, ErrorIDFormat
	}

	idx := strings.LastIndex(id, "_")
	prefix, ulidString := id[:idx], id[idx+1:]

	u, err := ulid.ParseStrict(ulidString)
	if err != nil {
		return nil, err
	}

	return &ID{
		prefix: prefix,
		value:  u,
	}, nil
}

// MustParse generates a new Appio short ID from a string or wil panic if any
// error encountered; mimicking uuid.MustParse from the google/uuid package.
func MustParse(id string) *ID {
	u, err := Parse(id)
	if err != nil {
		panic(`appioID: Parse(` + id + `): ` + err.Error())
	}

	return u
}

// Type extracts the prefix type of this Appio short ID.
func (id *ID) Type() string {
	return id.prefix
}

func (id *ID) Time() time.Time {
	return ulid.Time(id.value.Time())
}

// String
func (id *ID) String() string {
	return fmt.Sprintf("%s_%s", id.prefix, strings.ToLower(id.value.String()))
}

func (id *ID) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("%q", id.String())), nil
}

func (id *ID) UnmarshalJSON(bytes []byte) error {
	idStr, err := strconv.Unquote(string(bytes))
	if err != nil {
		return err
	}

	newID, err := Parse(idStr)
	if err != nil {
		return err
	}

	id.prefix = newID.prefix
	id.value = newID.value

	return nil
}

func (id *ID) Scan(value interface{}) error {
	switch v := value.(type) {
	case string:
		parsedId, err := Parse(v)
		if err != nil {
			return err
		}
		id.prefix = parsedId.prefix
		id.value = parsedId.value
		return nil
	default:
		return fmt.Errorf("unsupported type: %T", v)
	}
}
