go-appioid
===========

A Appio ID takes the format `abc_xxxxxxxxxxxxxxxxxxxxxxxxxx` / `^[a-z_]{2,10}_[a-z\d]{26}$`

This is a combination of a short representation of an entity type and a ULID.

All IDs used in Appio should be of this format

They should be stored as-is in a database with column type of `text` (Postgres).

Usage
-----

```go
s, err := appioid.New("prod")

fmt.Println(s) // Output: prod_01JJD5JMDEBD8X7CQ4N97GKBN5
```

```go
appioIDString := "prod_01JJD5J7JM9YNF2HZXWK0C6T2X"
s, err := appioid.Parse(appioIDString)

fmt.Println(s.Type()) // prod
fmt.Println(s.Time()) // 2024-01-24 23:06:10.847 +0100 BST
```

Setup
-----

```shell
echo "export GOPRIVATE=github.com/appio-so/*" >> ~/.bash_profile && \
source ~/.bash_profile

git config --global url."**************:".insteadOf "https://github.com/"
```